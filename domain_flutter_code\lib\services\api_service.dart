import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/constants.dart';

class ApiService {
  static const String _baseUrl = ApiConstants.baseUrl;
  static const Duration _timeout = Duration(seconds: 30);

  static Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(StorageKeys.authToken);
  }

  static Future<Map<String, String>> _getHeaders({
    bool requiresAuth = true,
  }) async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (requiresAuth) {
      final token = await _getToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  static Future<Map<String, dynamic>> _handleResponse(
    http.Response response,
  ) async {
    try {
      final data = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return data;
      } else {
        // Handle different error types
        String errorMessage = 'An error occurred';

        if (data is Map<String, dynamic>) {
          if (data.containsKey('message')) {
            errorMessage = data['message'];
          } else if (data.containsKey('errors')) {
            // Handle validation errors
            final errors = data['errors'];
            if (errors is Map<String, dynamic>) {
              final firstError = errors.values.first;
              if (firstError is List && firstError.isNotEmpty) {
                errorMessage = firstError.first.toString();
              } else if (firstError is String) {
                errorMessage = firstError;
              }
            } else if (errors is String) {
              errorMessage = errors;
            }
          } else if (response.statusCode == 422) {
            errorMessage = 'Invalid credentials provided';
          } else if (response.statusCode == 401) {
            errorMessage = 'Unauthorized access';
          } else if (response.statusCode == 500) {
            errorMessage = 'Server error. Please try again later.';
          }
        }

        throw Exception(errorMessage);
      }
    } catch (e) {
      if (e is FormatException) {
        throw Exception('Invalid response format from server');
      }
      rethrow;
    }
  }

  // Auth endpoints
  static Future<Map<String, dynamic>> login(
    String email,
    String password,
  ) async {
    try {
      final uri = Uri.parse('$_baseUrl${ApiConstants.loginEndpoint}');
      final headers = await _getHeaders(requiresAuth: false);
      final body = json.encode({'email': email.trim(), 'password': password});

      final response = await http
          .post(uri, headers: headers, body: body)
          .timeout(_timeout);

      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection. Please check your network.');
    } on HttpException {
      throw Exception('Server error. Please try again later.');
    } on FormatException {
      throw Exception('Invalid response from server.');
    } catch (e) {
      if (e.toString().contains('TimeoutException')) {
        throw Exception('Connection timeout. Please try again.');
      }
      rethrow;
    }
  }

  static Future<Map<String, dynamic>> logout() async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl${ApiConstants.logoutEndpoint}'),
            headers: await _getHeaders(),
          )
          .timeout(_timeout);

      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection');
    } catch (e) {
      rethrow;
    }
  }

  static Future<Map<String, dynamic>> getUser() async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl${ApiConstants.userEndpoint}'),
            headers: await _getHeaders(),
          )
          .timeout(_timeout);

      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection');
    } catch (e) {
      rethrow;
    }
  }

  // Domain endpoints
  static Future<Map<String, dynamic>> getDomains({
    int page = 1,
    String? search,
    int? categoryId,
    int? rating,
    List<String>? extensions,
  }) async {
    try {
      final queryParams = <String, String>{'page': page.toString()};

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (categoryId != null) {
        queryParams['category_id'] = categoryId.toString();
      }
      if (rating != null) {
        queryParams['rating'] = rating.toString();
      }
      if (extensions != null && extensions.isNotEmpty) {
        queryParams['extensions'] = extensions.join(',');
      }

      final uri = Uri.parse(
        '$_baseUrl${ApiConstants.domainsEndpoint}',
      ).replace(queryParameters: queryParams);

      final response = await http
          .get(uri, headers: await _getHeaders())
          .timeout(_timeout);

      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection');
    } catch (e) {
      rethrow;
    }
  }

  static Future<Map<String, dynamic>> createDomain(
    Map<String, dynamic> data,
  ) async {
    final response = await http.post(
      Uri.parse('$_baseUrl${ApiConstants.domainsEndpoint}'),
      headers: await _getHeaders(),
      body: json.encode(data),
    );

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> updateDomain(
    int id,
    Map<String, dynamic> data,
  ) async {
    final response = await http.put(
      Uri.parse('$_baseUrl${ApiConstants.domainsEndpoint}/$id'),
      headers: await _getHeaders(),
      body: json.encode(data),
    );

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> deleteDomain(int id) async {
    final response = await http.delete(
      Uri.parse('$_baseUrl${ApiConstants.domainsEndpoint}/$id'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  // Simple Domain endpoints
  static Future<Map<String, dynamic>> getSimpleDomains({
    int page = 1,
    String? search,
    int? categoryId,
  }) async {
    try {
      final queryParams = <String, String>{'page': page.toString()};

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (categoryId != null) {
        queryParams['category_id'] = categoryId.toString();
      }

      final uri = Uri.parse(
        '$_baseUrl${ApiConstants.simpleDomainsEndpoint}',
      ).replace(queryParameters: queryParams);

      final response = await http
          .get(uri, headers: await _getHeaders())
          .timeout(_timeout);

      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection');
    } catch (e) {
      rethrow;
    }
  }

  static Future<Map<String, dynamic>> createSimpleDomain(
    Map<String, dynamic> data,
  ) async {
    final response = await http.post(
      Uri.parse('$_baseUrl${ApiConstants.simpleDomainsEndpoint}'),
      headers: await _getHeaders(),
      body: json.encode(data),
    );

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> updateSimpleDomain(
    int id,
    Map<String, dynamic> data,
  ) async {
    final response = await http.put(
      Uri.parse('$_baseUrl${ApiConstants.simpleDomainsEndpoint}/$id'),
      headers: await _getHeaders(),
      body: json.encode(data),
    );

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> deleteSimpleDomain(int id) async {
    final response = await http.delete(
      Uri.parse('$_baseUrl${ApiConstants.simpleDomainsEndpoint}/$id'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> buySimpleDomain(
    int id,
    Map<String, dynamic> data,
  ) async {
    final response = await http.post(
      Uri.parse('$_baseUrl${ApiConstants.simpleDomainsEndpoint}/$id/buy'),
      headers: await _getHeaders(),
      body: json.encode(data),
    );

    return _handleResponse(response);
  }

  // Category endpoints
  static Future<Map<String, dynamic>> getCategories() async {
    final response = await http.get(
      Uri.parse('$_baseUrl${ApiConstants.categoriesEndpoint}'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> createCategory(
    Map<String, dynamic> data,
  ) async {
    final response = await http.post(
      Uri.parse('$_baseUrl${ApiConstants.categoriesEndpoint}'),
      headers: await _getHeaders(),
      body: json.encode(data),
    );

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> updateCategory(
    int id,
    Map<String, dynamic> data,
  ) async {
    final response = await http.put(
      Uri.parse('$_baseUrl${ApiConstants.categoriesEndpoint}/$id'),
      headers: await _getHeaders(),
      body: json.encode(data),
    );

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> deleteCategory(int id) async {
    final response = await http.delete(
      Uri.parse('$_baseUrl${ApiConstants.categoriesEndpoint}/$id'),
      headers: await _getHeaders(),
    );

    return _handleResponse(response);
  }

  // Dashboard stats
  static Future<Map<String, dynamic>> getDashboardStats() async {
    try {
      final response = await http
          .get(
            Uri.parse('$_baseUrl${ApiConstants.dashboardStatsEndpoint}'),
            headers: await _getHeaders(),
          )
          .timeout(_timeout);

      return _handleResponse(response);
    } on SocketException {
      throw Exception('No internet connection');
    } catch (e) {
      rethrow;
    }
  }
}
